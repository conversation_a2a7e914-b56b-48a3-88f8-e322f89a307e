.PHONY: help install backend frontend tunnel

.DEFAULT_GOAL := help
PROJECT_NAME := paykka-duty

help: ## Show this help message
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  \033[36m%-15s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

run:
	@echo "🚀 Starting Django server..."
	@uv run manage.py runserver 0.0.0.0:8000

tunnel: ## Run cloudflared tunnel
	@echo "🚇 Starting cloudflared tunnel..."
	@cloudflared tunnel run --token `cloudflared tunnel token $(PROJECT_NAME)` --protocol http2

keep: ## Run Keep service
	@echo "🚀 Starting Keep service..."
	@docker compose --profile dev up -d keep
